import SubmitProject from "@/components/blocks/submit-project/simple";
import { Suspense } from "react";
import { setRequestLocale, getTranslations } from "next-intl/server";
import { auth } from "@/auth";
import { redirect } from "next/navigation";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "submitProject" });
  
  return {
    title: `${t("title")} - Claude Code Show`,
    description: t("description"),
  };
}

export default async function SubmitProjectPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  setRequestLocale(locale);
  
  // Check if user is authenticated
  const session = await auth();
  if (!session?.user) {
    redirect(`/${locale}/auth/signin`);
  }
  
  const t = await getTranslations({ locale, namespace: "submitProject" });

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted/30">
      <div className="mx-auto max-w-4xl px-4 py-16 sm:px-6 lg:px-8">
        <div className="mb-8 text-center">
          <h1 className="text-4xl font-bold tracking-tight sm:text-5xl">
            {t("title")}
          </h1>
          <p className="mt-4 text-lg text-muted-foreground">
            {t("description")}
          </p>
        </div>
        
        <Suspense fallback={<SubmitProjectSkeleton />}>
          <SubmitProject />
        </Suspense>
      </div>
    </div>
  );
}

function SubmitProjectSkeleton() {
  return (
    <div className="animate-pulse">
      <div className="rounded-lg border bg-card p-8">
        <div className="space-y-4">
          <div className="h-32 rounded bg-muted" />
          <div className="h-10 w-32 rounded bg-muted" />
        </div>
      </div>
    </div>
  );
}