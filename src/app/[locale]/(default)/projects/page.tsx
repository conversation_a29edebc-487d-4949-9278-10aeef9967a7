import ProjectsList from "@/components/blocks/projects-list";
import ProjectsFilter from "@/components/blocks/projects-filter";
import { Suspense } from "react";
import { setRequestLocale, getTranslations } from "next-intl/server";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "projects" });
  
  return {
    title: `${t("title")} - Claude Code Show`,
    description: t("description"),
  };
}

export default async function ProjectsPage({
  params,
  searchParams,
}: {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{ search?: string; tag?: string; sort?: string; page?: string }>;
}) {
  const { locale } = await params;
  setRequestLocale(locale);
  
  const t = await getTranslations({ locale, namespace: "projects" });
  const { search, tag, sort = "latest", page = "1" } = await searchParams;

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted/30">
      <div className="mx-auto max-w-7xl px-4 py-16 sm:px-6 lg:px-8">
        <div className="mb-8 text-center">
          <h1 className="text-4xl font-bold tracking-tight sm:text-5xl">
            {t("title")}
          </h1>
          <p className="mt-4 text-lg text-muted-foreground">
            {t("description")}
          </p>
        </div>
        
        <ProjectsFilter />
        
        <Suspense fallback={<ProjectsListSkeleton />}>
          <ProjectsList 
            search={search}
            tag={tag}
            sort={sort}
            page={parseInt(page)}
          />
        </Suspense>
      </div>
    </div>
  );
}

function ProjectsListSkeleton() {
  return (
    <div className="mt-12 grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {[...Array(6)].map((_, i) => (
        <div key={i} className="animate-pulse">
          <div className="aspect-video rounded-lg bg-muted" />
          <div className="mt-4 space-y-2">
            <div className="h-4 w-3/4 rounded bg-muted" />
            <div className="h-3 w-full rounded bg-muted" />
            <div className="h-3 w-5/6 rounded bg-muted" />
          </div>
        </div>
      ))}
    </div>
  );
}