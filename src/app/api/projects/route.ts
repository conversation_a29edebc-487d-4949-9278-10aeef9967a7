import { respData, respErr, respJson } from "@/lib/resp";
import { findProjects } from "@/models/project";

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    
    // Parse query parameters
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "12");
    const search = searchParams.get("search") || undefined;
    const tag = searchParams.get("tag") || undefined;
    const sort = (searchParams.get("sort") || "latest") as any;

    // Validate pagination
    if (page < 1 || limit < 1 || limit > 100) {
      return respErr("Invalid pagination parameters");
    }

    // Get projects with filters
    const result = await findProjects(
      { search, tag, status: "published" },
      { page, limit, sort }
    );

    // Transform data to include parsed tags
    const transformedData = result.data.map(project => ({
      ...project,
      tags: project.tags ? JSON.parse(project.tags) : [],
    }));

    return respData({
      ...result,
      data: transformedData,
    });
  } catch (e) {
    console.error("Get projects failed: ", e);
    return respErr("Failed to fetch projects");
  }
}