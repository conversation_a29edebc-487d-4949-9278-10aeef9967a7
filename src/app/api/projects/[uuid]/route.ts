import { respData, respErr, respJson } from "@/lib/resp";
import { 
  findProjectByUuid, 
  incrementProjectViews 
} from "@/models/project";
import { findConversationsByProject } from "@/models/conversation";
import { getUserUuid } from "@/services/user";
import { auth } from "@/auth";
import { db } from "@/db";
import { orders, users } from "@/db/schema";
import { and, eq, gte } from "drizzle-orm";

interface RouteParams {
  params: Promise<{ uuid: string }>;
}

export async function GET(req: Request, { params }: RouteParams) {
  try {
    const { uuid } = await params;
    
    // Get project details
    const project = await findProjectByUuid(uuid);
    if (!project) {
      return respErr("Project not found");
    }

    // Increment view count
    await incrementProjectViews(uuid);

    // Parse tags
    const projectData = {
      ...project,
      tags: project.tags ? JSON.parse(project.tags) : [],
      viewsCount: project.viewsCount + 1, // Include the incremented view
    };

    // Get user info for author details
    const [author] = await db
      .select({
        name: users.nickname,
        email: users.email,
        avatar: users.avatarUrl,
      })
      .from(users)
      .where(eq(users.uuid, project.userUuid));

    // Check if current user is subscribed
    const session = await auth();
    let isSubscribed = false;
    let conversationLimit = 5; // Default limit for free users

    if (session?.user?.email) {
      const userUuid = await getUserUuid();
      if (userUuid) {
        // Check if user has active subscription
        const [activeOrder] = await db
          .select()
          .from(orders)
          .where(
            and(
              eq(orders.userUuid, userUuid),
              eq(orders.status, "paid"),
              gte(orders.expiredAt, new Date())
            )
          );
        
        isSubscribed = !!activeOrder;
        if (isSubscribed) {
          conversationLimit = 0; // No limit for subscribed users
        }
      }
    }

    // Get conversations with limit
    const conversationsResult = await findConversationsByProject(
      uuid,
      conversationLimit > 0 ? conversationLimit : undefined
    );

    return respData({
      project: {
        ...projectData,
        author: author || { name: "Anonymous", email: "", avatar: null },
      },
      conversations: conversationsResult.data,
      hasMoreConversations: conversationsResult.hasMore,
      totalConversations: conversationsResult.totalCount,
      isSubscribed,
    });
  } catch (e) {
    console.error("Get project details failed: ", e);
    return respErr("Failed to fetch project details");
  }
}