import { respData, respErr, respJson } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { auth } from "@/auth";
import { JsonlParser } from "@/services/jsonl-parser";
import { createProject } from "@/models/project";
import { createConversations } from "@/models/conversation";
import { v4 as uuidv4 } from "uuid";

export async function POST(req: Request) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return respJson(-2, "Authentication required");
    }

    const userUuid = await getUserUuid();
    if (!userUuid) {
      return respJson(-2, "User not found");
    }

    // Parse form data
    const formData = await req.formData();
    const file = formData.get('file') as File;
    const title = formData.get('title') as string;
    const description = formData.get('description') as string;
    const tags = formData.get('tags') as string;

    if (!file) {
      return respErr("No file provided");
    }

    // Validate file type
    if (!file.name.endsWith('.jsonl')) {
      return respErr("Invalid file format. Please upload a .jsonl file");
    }

    // Read file content
    const content = await file.text();

    try {
      // Parse JSONL content
      const parsedData = JsonlParser.parseJsonl(content);
      
      // Create project
      const project = await createProject({
        userUuid,
        title: title || parsedData.title,
        description: description || parsedData.description,
        tags: tags ? JSON.parse(tags) : parsedData.tags,
        promptsCount: parsedData.promptsCount,
        devTime: parsedData.devTime,
        status: "published",
        thumbnailUrl: `/imgs/showcases/${Math.floor(Math.random() * 9) + 1}.png` // Random placeholder
      });

      // Create conversations
      const conversationData = parsedData.conversations.map((conv, index) => ({
        projectUuid: project.uuid,
        sequence: index + 1,
        role: conv.role as "user" | "assistant",
        content: conv.content,
        code: conv.code,
        annotations: conv.annotations
      }));

      await createConversations(conversationData);

      return respData({
        projectId: project.uuid,
        title: project.title,
        conversationsCount: conversationData.length
      });

    } catch (parseError) {
      console.error("Parse error:", parseError);
      return respErr(parseError instanceof Error ? parseError.message : "Failed to parse JSONL file");
    }

  } catch (error) {
    console.error("Submit project failed:", error);
    return respErr("Failed to submit project");
  }
}

// Set max request size to 10MB
export const runtime = 'nodejs';
export const maxDuration = 60; // 60 seconds timeout