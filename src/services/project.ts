import { getUserUuid } from "./user";
import { db } from "@/db";
import { orders } from "@/db/schema";
import { and, eq, gte } from "drizzle-orm";

export async function checkUserSubscription(): Promise<boolean> {
  try {
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return false;
    }

    // Check if user has any active paid order
    const [activeOrder] = await db
      .select()
      .from(orders)
      .where(
        and(
          eq(orders.userUuid, userUuid),
          eq(orders.status, "paid"),
          gte(orders.expiredAt, new Date())
        )
      )
      .limit(1);

    return !!activeOrder;
  } catch (error) {
    console.error("Check user subscription failed:", error);
    return false;
  }
}

export function getConversationLimit(isSubscribed: boolean): number {
  return isSubscribed ? 0 : 5; // 0 means no limit
}