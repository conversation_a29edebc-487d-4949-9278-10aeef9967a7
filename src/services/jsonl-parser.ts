interface JsonlMessage {
  type?: string;
  message?: {
    role: 'user' | 'assistant' | 'system';
    content: string;
  };
  timestamp?: string;
  error?: any;
  cwd?: string;
}

interface ParsedConversation {
  role: 'user' | 'assistant';
  content: string;
  timestamp?: string;
  code?: string;
  annotations?: string[];
}

interface ParsedProject {
  title: string;
  description: string;
  conversations: ParsedConversation[];
  tags: string[];
  promptsCount: number;
  devTime: string;
}

export class JsonlParser {
  /**
   * Parse JSONL content and extract project information
   */
  static parseJsonl(content: string): ParsedProject {
    const lines = content.split('\n').filter(line => line.trim());
    const conversations: ParsedConversation[] = [];
    let firstUserMessage = '';
    let projectTitle = '';
    let startTime: Date | null = null;
    let endTime: Date | null = null;

    for (const line of lines) {
      try {
        const data: JsonlMessage = JSON.parse(line);
        
        // Extract timestamp
        if (data.timestamp && !startTime) {
          startTime = new Date(data.timestamp);
        }
        if (data.timestamp) {
          endTime = new Date(data.timestamp);
        }

        // Skip system messages and command outputs
        if (data.message && data.message.role && this.isValidMessage(data.message.content)) {
          const conversation: ParsedConversation = {
            role: data.message.role === 'user' ? 'user' : 'assistant',
            content: data.message.content,
            timestamp: data.timestamp
          };

          // Extract code blocks from assistant messages
          if (data.message.role === 'assistant') {
            const { content: cleanContent, code, annotations } = this.extractCodeAndAnnotations(data.message.content);
            conversation.content = cleanContent;
            if (code) conversation.code = code;
            if (annotations.length > 0) conversation.annotations = annotations;
          }

          conversations.push(conversation);

          // Capture first user message for title/description
          if (!firstUserMessage && data.message.role === 'user') {
            firstUserMessage = data.message.content;
            projectTitle = this.extractTitle(firstUserMessage);
          }
        }
      } catch (e) {
        // Skip invalid JSON lines
        console.warn('Failed to parse line:', e);
      }
    }

    if (conversations.length === 0) {
      throw new Error('No valid conversations found in the file');
    }

    // Calculate development time
    const devTime = this.calculateDevTime(startTime, endTime);

    // Extract tags from content
    const tags = this.extractTags(conversations);

    // Count prompts (user messages)
    const promptsCount = conversations.filter(c => c.role === 'user').length;

    return {
      title: projectTitle || 'Untitled Project',
      description: firstUserMessage || 'No description available',
      conversations,
      tags,
      promptsCount,
      devTime
    };
  }

  /**
   * Check if a message is valid (not a system message or command output)
   */
  private static isValidMessage(content: string): boolean {
    if (!content) return false;
    
    // Skip messages with warning caveat
    if (content.includes('Caveat: The messages below were generated by')) {
      return false;
    }

    // Skip command-related messages
    if (content.startsWith('<command-name>') || 
        content.startsWith('<local-command-stdout>') ||
        content.startsWith('<system-reminder>')) {
      return false;
    }

    return true;
  }

  /**
   * Extract title from first user message
   */
  private static extractTitle(message: string): string {
    // Take first line, limit to 100 characters
    const firstLine = message.split('\n')[0];
    return firstLine.substring(0, 100).trim();
  }

  /**
   * Extract code blocks and annotations from assistant message
   */
  private static extractCodeAndAnnotations(content: string): {
    content: string;
    code?: string;
    annotations: string[];
  } {
    const annotations: string[] = [];
    let code: string | undefined;
    let cleanContent = content;

    // Extract code blocks (```...```)
    const codeBlockRegex = /```[\s\S]*?```/g;
    const codeBlocks = content.match(codeBlockRegex);
    
    if (codeBlocks && codeBlocks.length > 0) {
      // Take the first major code block
      code = codeBlocks[0].replace(/```\w*\n?/g, '').trim();
      // Remove code blocks from content for cleaner display
      cleanContent = content.replace(codeBlockRegex, '[Code block]');
    }

    // Extract potential annotations (lines starting with "Note:", "Important:", etc.)
    const annotationRegex = /^(Note|Important|Key insight|Best practice|Warning|Tip):\s*(.+)$/gim;
    let match;
    while ((match = annotationRegex.exec(content)) !== null) {
      annotations.push(match[2].trim());
    }

    return { content: cleanContent, code, annotations };
  }

  /**
   * Extract tags from conversations
   */
  private static extractTags(conversations: ParsedConversation[]): string[] {
    const tags = new Set<string>();
    const allContent = conversations.map(c => c.content).join(' ').toLowerCase();

    // Technology keywords to look for
    const techKeywords = [
      // Frontend
      'React', 'Vue', 'Angular', 'Svelte', 'Next.js', 'Nuxt', 'Gatsby',
      // Languages
      'TypeScript', 'JavaScript', 'Python', 'Java', 'Go', 'Rust', 'C++', 'C#', 'Ruby', 'PHP',
      // Backend
      'Node.js', 'Express', 'FastAPI', 'Django', 'Flask', 'Spring', 'Rails',
      // Databases
      'PostgreSQL', 'MySQL', 'MongoDB', 'Redis', 'SQLite', 'DynamoDB',
      // Cloud/DevOps
      'Docker', 'Kubernetes', 'AWS', 'Azure', 'GCP', 'Vercel', 'Netlify', 'Cloudflare',
      // Others
      'GraphQL', 'REST API', 'WebSocket', 'gRPC', 'Tailwind', 'CSS', 'HTML', 'AI', 'Machine Learning'
    ];

    techKeywords.forEach(keyword => {
      if (allContent.includes(keyword.toLowerCase())) {
        tags.add(keyword);
      }
    });

    // Limit to top 5 tags
    return Array.from(tags).slice(0, 5);
  }

  /**
   * Calculate development time from timestamps
   */
  private static calculateDevTime(startTime: Date | null, endTime: Date | null): string {
    if (!startTime || !endTime) return '1h';

    const diffMs = endTime.getTime() - startTime.getTime();
    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    if (hours === 0) {
      return `${minutes}m`;
    } else if (minutes === 0) {
      return `${hours}h`;
    } else {
      return `${hours}h ${minutes}m`;
    }
  }
}