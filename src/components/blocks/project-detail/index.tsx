"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import Icon from "@/components/icon";
import { useTranslations } from "next-intl";
import moment from "moment";
import "moment/locale/zh-cn";
import { useLocale } from "next-intl";
import Link from "next/link";
import { toast } from "sonner";

interface ProjectDetailProps {
  project: {
    uuid: string;
    title: string;
    description?: string | null;
    tags: string[];
    promptsCount: number;
    devTime?: string | null;
    likesCount: number;
    viewsCount: number;
    createdAt: Date;
    conversations: Array<{
      uuid: string;
      sequence: number;
      role: string;
      content: string;
      code?: string | null;
      annotations?: string | null;
    }>;
    isLimited: boolean;
  };
}

export default function ProjectDetail({ project }: ProjectDetailProps) {
  const t = useTranslations("projects");
  const locale = useLocale();
  
  // Set moment locale
  React.useEffect(() => {
    moment.locale(locale === "zh" ? "zh-cn" : "en");
  }, [locale]);
  
  const handleLike = async () => {
    // TODO: Implement like functionality
    toast.success(t("liked"));
  };
  
  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: project.title,
        text: project.description || "",
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      toast.success(t("linkCopied"));
    }
  };
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted/30">
      <div className="mx-auto max-w-5xl px-4 py-8 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <Link href={`/${locale}/projects`} className="inline-flex items-center gap-2 text-muted-foreground hover:text-foreground mb-4">
            <Icon name="RiArrowLeftLine" className="h-4 w-4" />
            {t("backToProjects")}
          </Link>
          
          <h1 className="text-4xl font-bold tracking-tight mb-4">{project.title}</h1>
          
          {project.description && (
            <p className="text-lg text-muted-foreground mb-6">{project.description}</p>
          )}
          
          <div className="flex flex-wrap items-center gap-4 mb-6">
            {project.tags.map((tag) => (
              <Badge key={tag} variant="secondary">
                {tag}
              </Badge>
            ))}
          </div>
          
          <div className="flex flex-wrap items-center gap-6 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <Icon name="RiMessage3Line" className="h-4 w-4" />
              <span>{t("promptsCount", { count: project.promptsCount })}</span>
            </div>
            {project.devTime && (
              <div className="flex items-center gap-2">
                <Icon name="RiTimeLine" className="h-4 w-4" />
                <span>{project.devTime}</span>
              </div>
            )}
            <div className="flex items-center gap-2">
              <Icon name="RiEyeLine" className="h-4 w-4" />
              <span>{t("viewsCount", { count: project.viewsCount })}</span>
            </div>
            <div className="flex items-center gap-2">
              <Icon name="RiHeartLine" className="h-4 w-4" />
              <span>{t("likesCount", { count: project.likesCount })}</span>
            </div>
            <div className="flex items-center gap-2">
              <Icon name="RiCalendarLine" className="h-4 w-4" />
              <span>
                {moment(project.createdAt).fromNow()}
              </span>
            </div>
          </div>
          
          <div className="flex items-center gap-4 mt-6">
            <Button onClick={handleLike} variant="outline" size="sm">
              <Icon name="RiHeartLine" className="h-4 w-4 mr-2" />
              {t("like")}
            </Button>
            <Button onClick={handleShare} variant="outline" size="sm">
              <Icon name="RiShareLine" className="h-4 w-4 mr-2" />
              {t("share")}
            </Button>
          </div>
        </div>
        
        <Separator className="mb-8" />
        
        {/* Conversations */}
        <div className="space-y-6">
          <h2 className="text-2xl font-semibold">{t("conversationHistory")}</h2>
          
          {project.conversations.map((conv, index) => (
            <Card key={conv.uuid} className="overflow-hidden">
              <CardHeader className={conv.role === "user" ? "bg-muted/50" : "bg-primary/5"}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`rounded-full p-2 ${
                      conv.role === "user" ? "bg-primary/10" : "bg-primary"
                    }`}>
                      <Icon 
                        name={conv.role === "user" ? "RiUser3Line" : "RiRobotLine"} 
                        className={`h-5 w-5 ${
                          conv.role === "user" ? "text-primary" : "text-primary-foreground"
                        }`} 
                      />
                    </div>
                    <CardTitle className="text-lg">
                      {conv.role === "user" ? t("user") : t("assistant")}
                    </CardTitle>
                  </div>
                  <span className="text-sm text-muted-foreground">#{conv.sequence}</span>
                </div>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="prose prose-sm dark:prose-invert max-w-none">
                  <p className="whitespace-pre-wrap">{conv.content}</p>
                </div>
                
                {conv.code && (
                  <div className="mt-4">
                    <div className="rounded-lg bg-muted p-4 overflow-x-auto">
                      <pre className="text-sm">
                        <code>{conv.code}</code>
                      </pre>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
          
          {project.isLimited && (
            <Card className="border-dashed">
              <CardContent className="py-12 text-center">
                <Icon name="RiLockLine" className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">{t("moreConversationsLocked")}</h3>
                <p className="text-muted-foreground mb-4">{t("upgradeToViewAll")}</p>
                <Button asChild>
                  <Link href={`/${locale}/pricing`}>{t("upgradeToPro")}</Link>
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}