"use client";

import { useState, useCallback } from "react";
import { useDropzone } from "react-dropzone";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import Icon from "@/components/icon";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

interface ParsedProject {
  title: string;
  description: string;
  conversationsCount: number;
  firstMessage: string;
  tags: string[];
}

export default function SubmitProject() {
  const t = useTranslations("submitProject");
  const router = useRouter();
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [parsedData, setParsedData] = useState<ParsedProject | null>(null);
  const [error, setError] = useState<string | null>(null);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const uploadedFile = acceptedFiles[0];
    if (uploadedFile && uploadedFile.name.endsWith('.jsonl')) {
      setFile(uploadedFile);
      setError(null);
      setParsedData(null);
      // Start parsing immediately
      parseFile(uploadedFile);
    } else {
      setError(t("errors.invalidFile"));
    }
  }, [t]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/x-jsonlines': ['.jsonl'],
      'text/plain': ['.jsonl']
    },
    maxFiles: 1,
    multiple: false
  });

  const parseFile = async (fileToparse: File) => {
    setLoading(true);
    setProgress(10);
    
    try {
      const content = await fileToparse.text();
      const lines = content.split('\n').filter(line => line.trim());
      
      setProgress(30);
      
      // Parse conversations
      const conversations = [];
      let firstUserMessage = null;
      let projectTitle = "";
      
      for (const line of lines) {
        try {
          const data = JSON.parse(line);
          
          if (data.message && data.message.role) {
            // Skip system messages and command outputs
            if (data.message.content?.includes('Caveat: The messages below were generated by')) {
              continue;
            }
            
            conversations.push({
              role: data.message.role,
              content: data.message.content,
              timestamp: data.timestamp
            });
            
            // Capture first user message for title/description
            if (!firstUserMessage && data.message.role === 'user') {
              firstUserMessage = data.message.content;
              // Extract potential title from first message
              projectTitle = firstUserMessage.split('\n')[0].substring(0, 100);
            }
          }
        } catch (e) {
          // Skip invalid JSON lines
          continue;
        }
      }
      
      setProgress(60);
      
      if (conversations.length === 0) {
        throw new Error(t("errors.noConversations"));
      }
      
      // Extract potential tags from content
      const allContent = conversations.map(c => c.content).join(' ');
      const tags = extractTags(allContent);
      
      setProgress(80);
      
      setParsedData({
        title: projectTitle || t("defaultProjectTitle"),
        description: firstUserMessage || t("defaultProjectDescription"),
        conversationsCount: conversations.length,
        firstMessage: firstUserMessage || "",
        tags: tags
      });
      
      setProgress(100);
    } catch (err) {
      setError(err instanceof Error ? err.message : t("errors.parseFailed"));
    } finally {
      setLoading(false);
    }
  };

  const extractTags = (content: string): string[] => {
    const tags = new Set<string>();
    
    // Common technology keywords
    const techKeywords = [
      'React', 'Vue', 'Angular', 'Next.js', 'Node.js', 'TypeScript', 'JavaScript',
      'Python', 'Django', 'Flask', 'FastAPI', 'Java', 'Spring', 'Go', 'Rust',
      'Docker', 'Kubernetes', 'AWS', 'Azure', 'GCP', 'PostgreSQL', 'MongoDB',
      'Redis', 'GraphQL', 'REST API', 'WebSocket', 'Tailwind', 'CSS', 'HTML'
    ];
    
    techKeywords.forEach(keyword => {
      if (content.toLowerCase().includes(keyword.toLowerCase())) {
        tags.add(keyword);
      }
    });
    
    return Array.from(tags).slice(0, 5); // Limit to 5 tags
  };

  const handleSubmit = async () => {
    if (!file || !parsedData) return;
    
    setLoading(true);
    setProgress(0);
    
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('title', parsedData.title);
      formData.append('description', parsedData.description);
      formData.append('tags', JSON.stringify(parsedData.tags));
      
      setProgress(20);
      
      const response = await fetch('/api/projects/submit', {
        method: 'POST',
        body: formData,
      });
      
      setProgress(60);
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || t("errors.submitFailed"));
      }
      
      const result = await response.json();
      
      setProgress(100);
      
      toast.success(t("success.projectSubmitted"));
      
      // Redirect to the project detail page
      setTimeout(() => {
        router.push(`/projects/${result.data.projectId}`);
      }, 1000);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : t("errors.submitFailed"));
      toast.error(t("errors.submitFailed"));
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    setFile(null);
    setParsedData(null);
    setError(null);
    setProgress(0);
  };

  return (
    <div className="space-y-6">
      {!parsedData ? (
        <Card>
          <CardHeader>
            <CardTitle>{t("uploadTitle")}</CardTitle>
            <CardDescription>{t("uploadDescription")}</CardDescription>
          </CardHeader>
          <CardContent>
            <div
              {...getRootProps()}
              className={`
                relative cursor-pointer rounded-lg border-2 border-dashed p-12 text-center transition-colors
                ${isDragActive ? 'border-primary bg-primary/5' : 'border-muted-foreground/25 hover:border-primary'}
                ${loading ? 'pointer-events-none opacity-50' : ''}
              `}
            >
              <input {...getInputProps()} />
              
              <Icon 
                name="RiUploadCloudLine" 
                className="mx-auto h-12 w-12 text-muted-foreground" 
              />
              
              <p className="mt-4 text-sm font-medium">
                {isDragActive ? t("dropFile") : t("dragOrClick")}
              </p>
              
              <p className="mt-2 text-xs text-muted-foreground">
                {t("fileFormat")}
              </p>
              
              {file && (
                <div className="mt-4 inline-flex items-center gap-2 rounded-md bg-muted px-3 py-1">
                  <Icon name="RiFileTextLine" className="h-4 w-4" />
                  <span className="text-sm">{file.name}</span>
                </div>
              )}
            </div>
            
            {loading && (
              <div className="mt-4">
                <Progress value={progress} className="h-2" />
                <p className="mt-2 text-center text-sm text-muted-foreground">
                  {t("parsing")}
                </p>
              </div>
            )}
            
            {error && (
              <Alert variant="destructive" className="mt-4">
                <Icon name="RiErrorWarningLine" className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      ) : (
        <>
          <Card>
            <CardHeader>
              <CardTitle>{t("previewTitle")}</CardTitle>
              <CardDescription>{t("previewDescription")}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">{t("projectTitle")}</label>
                <p className="mt-1 text-sm text-muted-foreground">{parsedData.title}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium">{t("projectDescription")}</label>
                <p className="mt-1 text-sm text-muted-foreground line-clamp-3">
                  {parsedData.description}
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium">{t("conversationsCount")}</label>
                <p className="mt-1 text-sm text-muted-foreground">
                  {parsedData.conversationsCount} {t("conversations")}
                </p>
              </div>
              
              {parsedData.tags.length > 0 && (
                <div>
                  <label className="text-sm font-medium">{t("detectedTags")}</label>
                  <div className="mt-2 flex flex-wrap gap-2">
                    {parsedData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center rounded-md bg-primary/10 px-2 py-1 text-xs font-medium text-primary"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
          
          <div className="flex gap-4">
            <Button
              variant="outline"
              onClick={handleReset}
              disabled={loading}
            >
              <Icon name="RiArrowLeftLine" className="mr-2 h-4 w-4" />
              {t("uploadAnother")}
            </Button>
            
            <Button
              onClick={handleSubmit}
              disabled={loading}
              className="flex-1"
            >
              {loading ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  {t("submitting")}
                </>
              ) : (
                <>
                  <Icon name="RiCheckLine" className="mr-2 h-4 w-4" />
                  {t("submitProject")}
                </>
              )}
            </Button>
          </div>
          
          {loading && (
            <Progress value={progress} className="h-2" />
          )}
        </>
      )}
      
      <Alert>
        <Icon name="RiInformationLine" className="h-4 w-4" />
        <AlertDescription>
          {t("privacyNote")}
        </AlertDescription>
      </Alert>
    </div>
  );
}