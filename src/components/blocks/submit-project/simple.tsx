"use client";

import { useState, useRef } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import Icon from "@/components/icon";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

// Simple version without react-dropzone dependency
export default function SubmitProjectSimple() {
  const t = useTranslations("submitProject");
  const router = useRouter();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile && selectedFile.name.endsWith('.jsonl')) {
      setFile(selectedFile);
      setError(null);
      handleSubmit(selectedFile);
    } else {
      setError(t("errors.invalidFile"));
    }
  };

  const handleSubmit = async (fileToSubmit: File) => {
    setLoading(true);
    setProgress(20);
    
    try {
      const formData = new FormData();
      formData.append('file', fileToSubmit);
      
      setProgress(50);
      
      const response = await fetch('/api/submit-project', {
        method: 'POST',
        body: formData,
      });
      
      setProgress(80);
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || t("errors.submitFailed"));
      }
      
      const result = await response.json();
      
      setProgress(100);
      
      toast.success(t("success.projectSubmitted"));
      
      // Redirect to the project detail page
      setTimeout(() => {
        router.push(`/projects/${result.data.projectId}`);
      }, 1000);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : t("errors.submitFailed"));
      toast.error(t("errors.submitFailed"));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("uploadTitle")}</CardTitle>
        <CardDescription>{t("uploadDescription")}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-center w-full">
            <label
              htmlFor="jsonl-upload"
              className="flex flex-col items-center justify-center w-full h-64 border-2 border-dashed rounded-lg cursor-pointer bg-muted/50 hover:bg-muted"
            >
              <div className="flex flex-col items-center justify-center pt-5 pb-6">
                <Icon name="RiUploadCloudLine" className="w-10 h-10 mb-3 text-muted-foreground" />
                <p className="mb-2 text-sm text-muted-foreground">
                  <span className="font-semibold">{t("dragOrClick")}</span>
                </p>
                <p className="text-xs text-muted-foreground">{t("fileFormat")}</p>
              </div>
              <input
                id="jsonl-upload"
                ref={fileInputRef}
                type="file"
                className="hidden"
                accept=".jsonl"
                onChange={handleFileChange}
                disabled={loading}
              />
            </label>
          </div>
          
          {file && (
            <div className="flex items-center gap-2 p-3 rounded-md bg-muted">
              <Icon name="RiFileTextLine" className="h-4 w-4" />
              <span className="text-sm">{file.name}</span>
            </div>
          )}
          
          {loading && (
            <div>
              <Progress value={progress} className="h-2" />
              <p className="mt-2 text-center text-sm text-muted-foreground">
                {t("submitting")}
              </p>
            </div>
          )}
          
          {error && (
            <Alert variant="destructive">
              <Icon name="RiErrorWarningLine" className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </div>
        
        <Alert className="mt-4">
          <Icon name="RiInformationLine" className="h-4 w-4" />
          <AlertDescription>
            {t("privacyNote")}
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
}