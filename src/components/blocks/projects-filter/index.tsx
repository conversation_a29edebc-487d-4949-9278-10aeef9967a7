"use client";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Icon from "@/components/icon";
import { useRouter, useSearchParams } from "next/navigation";
import React, { useState, useCallback } from "react";
import { useDebounce } from "@/hooks/use-debounce";
import { useTranslations } from "next-intl";

const popularTags = [
  "React",
  "Next.js",
  "TypeScript",
  "Tailwind",
  "Node.js",
  "Python",
  "AI/ML",
  "Database",
  "API",
  "Full-stack",
];

export default function ProjectsFilter() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const t = useTranslations("projects");
  
  const [search, setSearch] = useState(searchParams.get("search") || "");
  const debouncedSearch = useDebounce(search, 500);
  
  const currentTag = searchParams.get("tag") || "";
  const currentSort = searchParams.get("sort") || "latest";

  const updateParams = useCallback((key: string, value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    if (value) {
      params.set(key, value);
    } else {
      params.delete(key);
    }
    // Reset to page 1 when filters change
    params.set("page", "1");
    router.push(`/projects?${params.toString()}`);
  }, [router, searchParams]);

  // Update search param when debounced value changes
  React.useEffect(() => {
    updateParams("search", debouncedSearch);
  }, [debouncedSearch, updateParams]);

  const handleTagClick = (tag: string) => {
    if (currentTag === tag) {
      updateParams("tag", "");
    } else {
      updateParams("tag", tag);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="relative flex-1 max-w-md">
          <Icon 
            name="RiSearchLine" 
            className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" 
          />
          <Input
            type="search"
            placeholder={t("searchPlaceholder")}
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={currentSort} onValueChange={(value) => updateParams("sort", value)}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder={t("sortBy")} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="latest">{t("sortOptions.latest")}</SelectItem>
            <SelectItem value="popular">{t("sortOptions.popular")}</SelectItem>
            <SelectItem value="prompts">{t("sortOptions.prompts")}</SelectItem>
            <SelectItem value="likes">{t("sortOptions.likes")}</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="flex flex-wrap gap-2">
        {popularTags.map((tag) => (
          <Badge
            key={tag}
            variant={currentTag === tag ? "default" : "outline"}
            className="cursor-pointer transition-colors hover:bg-primary hover:text-primary-foreground"
            onClick={() => handleTagClick(tag)}
          >
            {tag}
          </Badge>
        ))}
        {currentTag && !popularTags.includes(currentTag) && (
          <Badge variant="default">
            {currentTag}
            <Icon 
              name="RiCloseLine" 
              className="ml-1 h-3 w-3 cursor-pointer" 
              onClick={() => updateParams("tag", "")}
            />
          </Badge>
        )}
      </div>
    </div>
  );
}