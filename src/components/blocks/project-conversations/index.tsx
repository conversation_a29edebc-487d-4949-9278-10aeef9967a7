"use client";

import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import Icon from "@/components/icon";
import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";

interface Conversation {
  id: number;
  uuid: string;
  sequence: number;
  role: "user" | "assistant";
  content: string;
  code?: string;
  annotations?: string[];
  createdAt: string;
}

interface ProjectConversationsProps {
  projectId: string;
  isSubscribed: boolean;
}

export default function ProjectConversations({ projectId, isSubscribed }: ProjectConversationsProps) {
  const t = useTranslations("projectDetail");
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(true);
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);

  useEffect(() => {
    // TODO: Replace with actual API call
    const fetchConversations = async () => {
      setLoading(true);
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const mockConversations: Conversation[] = [
        {
          id: 1,
          uuid: "conv-1",
          sequence: 1,
          role: "user",
          content: "Create a React component for a todo list with add, delete, and mark complete features. Use TypeScript and Tailwind CSS for styling.",
          createdAt: new Date().toISOString(),
        },
        {
          id: 2,
          uuid: "conv-2",
          sequence: 2,
          role: "assistant",
          content: "I'll create a comprehensive Todo List component with all the requested features. This will include TypeScript for type safety and Tailwind CSS for modern styling.",
          code: `import React, { useState } from 'react';

interface Todo {
  id: number;
  text: string;
  completed: boolean;
}

const TodoList: React.FC = () => {
  const [todos, setTodos] = useState<Todo[]>([]);
  const [inputValue, setInputValue] = useState('');

  const addTodo = () => {
    if (inputValue.trim()) {
      setTodos([...todos, {
        id: Date.now(),
        text: inputValue,
        completed: false
      }]);
      setInputValue('');
    }
  };

  const deleteTodo = (id: number) => {
    setTodos(todos.filter(todo => todo.id !== id));
  };

  const toggleComplete = (id: number) => {
    setTodos(todos.map(todo =>
      todo.id === id ? { ...todo, completed: !todo.completed } : todo
    ));
  };

  return (
    <div className="max-w-md mx-auto mt-8 p-6 bg-white rounded-lg shadow-md">
      <h1 className="text-2xl font-bold mb-4">Todo List</h1>
      
      <div className="flex mb-4">
        <input
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && addTodo()}
          className="flex-1 px-3 py-2 border rounded-l-md focus:outline-none"
          placeholder="Add a new todo..."
        />
        <button
          onClick={addTodo}
          className="px-4 py-2 bg-blue-500 text-white rounded-r-md hover:bg-blue-600"
        >
          Add
        </button>
      </div>
      
      <ul className="space-y-2">
        {todos.map(todo => (
          <li key={todo.id} className="flex items-center p-2 border rounded">
            <input
              type="checkbox"
              checked={todo.completed}
              onChange={() => toggleComplete(todo.id)}
              className="mr-2"
            />
            <span className={\`flex-1 \${todo.completed ? 'line-through text-gray-500' : ''}\`}>
              {todo.text}
            </span>
            <button
              onClick={() => deleteTodo(todo.id)}
              className="px-2 py-1 text-red-500 hover:bg-red-100 rounded"
            >
              Delete
            </button>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default TodoList;`,
          annotations: ["Uses React Hooks for state management", "TypeScript interfaces for type safety", "Tailwind CSS for responsive design"],
          createdAt: new Date().toISOString(),
        },
        {
          id: 3,
          uuid: "conv-3",
          sequence: 3,
          role: "user",
          content: "Can you add local storage persistence to save the todos?",
          createdAt: new Date().toISOString(),
        },
        {
          id: 4,
          uuid: "conv-4",
          sequence: 4,
          role: "assistant",
          content: "I'll add local storage functionality to persist the todos across browser sessions.",
          code: `// Add this useEffect hook after the useState declarations:

useEffect(() => {
  const savedTodos = localStorage.getItem('todos');
  if (savedTodos) {
    setTodos(JSON.parse(savedTodos));
  }
}, []);

useEffect(() => {
  localStorage.setItem('todos', JSON.stringify(todos));
}, [todos]);`,
          annotations: ["useEffect for side effects", "JSON serialization for storage", "Automatic save on state change"],
          createdAt: new Date().toISOString(),
        },
        {
          id: 5,
          uuid: "conv-5",
          sequence: 5,
          role: "user",
          content: "Add a filter to show all, active, or completed todos",
          createdAt: new Date().toISOString(),
        },
        {
          id: 6,
          uuid: "conv-6",
          sequence: 6,
          role: "assistant",
          content: "I'll add filtering functionality with buttons to switch between all, active, and completed todos.",
          code: `// Add this state and filter logic:

const [filter, setFilter] = useState<'all' | 'active' | 'completed'>('all');

const filteredTodos = todos.filter(todo => {
  if (filter === 'active') return !todo.completed;
  if (filter === 'completed') return todo.completed;
  return true;
});

// Add filter buttons before the todo list:
<div className="flex gap-2 mb-4">
  <button
    onClick={() => setFilter('all')}
    className={\`px-3 py-1 rounded \${filter === 'all' ? 'bg-blue-500 text-white' : 'bg-gray-200'}\`}
  >
    All ({todos.length})
  </button>
  <button
    onClick={() => setFilter('active')}
    className={\`px-3 py-1 rounded \${filter === 'active' ? 'bg-blue-500 text-white' : 'bg-gray-200'}\`}
  >
    Active ({todos.filter(t => !t.completed).length})
  </button>
  <button
    onClick={() => setFilter('completed')}
    className={\`px-3 py-1 rounded \${filter === 'completed' ? 'bg-blue-500 text-white' : 'bg-gray-200'}\`}
  >
    Completed ({todos.filter(t => t.completed).length})
  </button>
</div>`,
          createdAt: new Date().toISOString(),
        },
        {
          id: 7,
          uuid: "conv-7",
          sequence: 7,
          role: "user",
          content: "Perfect! Can you show me the complete final code?",
          createdAt: new Date().toISOString(),
        },
        {
          id: 8,
          uuid: "conv-8",
          sequence: 8,
          role: "assistant",
          content: "Here's the complete Todo List component with all the features we've implemented:",
          code: `// Complete TodoList.tsx implementation with all features...`,
          createdAt: new Date().toISOString(),
        },
      ];
      
      // For non-subscribed users, limit to first 5 conversations
      const limitedConversations = isSubscribed 
        ? mockConversations 
        : mockConversations.slice(0, 5);
      
      setConversations(limitedConversations);
      setLoading(false);
    };

    fetchConversations();
  }, [projectId, isSubscribed]);

  const handleCopyCode = (code: string, index: number) => {
    navigator.clipboard.writeText(code);
    setCopiedIndex(index);
    setTimeout(() => setCopiedIndex(null), 2000);
  };

  if (loading) {
    return null;
  }

  return (
    <div className="space-y-6">
      {conversations.map((conv, index) => (
        <Card
          key={conv.id}
          className={`p-6 ${
            conv.role === "user" 
              ? "ml-0 mr-auto max-w-4xl bg-muted/30" 
              : "ml-auto mr-0 max-w-4xl"
          }`}
        >
          <div className="flex items-start gap-4">
            <div className={`rounded-full p-2 ${
              conv.role === "user" 
                ? "bg-primary/10 text-primary" 
                : "bg-secondary/10 text-secondary-foreground"
            }`}>
              <Icon 
                name={conv.role === "user" ? "RiUserLine" : "RiRobot2Line"} 
                className="h-5 w-5" 
              />
            </div>
            
            <div className="flex-1 space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">
                  {conv.role === "user" ? t("developer") : t("claude")}
                </span>
                <span className="text-xs text-muted-foreground">
                  #{conv.sequence}
                </span>
              </div>
              
              <div className="prose prose-gray max-w-none dark:prose-invert">
                <p className="text-sm">{conv.content}</p>
              </div>
              
              {conv.code && (
                <div className="relative">
                  <div className="absolute right-2 top-2 z-10">
                    <Button
                      size="sm"
                      variant="ghost"
                      className="bg-background/80 backdrop-blur-sm"
                      onClick={() => handleCopyCode(conv.code!, index)}
                    >
                      <Icon name="RiFileCopyLine" className="mr-1 h-3 w-3" />
                      {copiedIndex === index ? t("codeCopied") : t("copyCode")}
                    </Button>
                  </div>
                  <pre className="overflow-x-auto rounded-lg bg-slate-900 p-4 text-xs">
                    <code className="language-typescript text-slate-100">
                      {conv.code}
                    </code>
                  </pre>
                </div>
              )}
              
              {conv.annotations && conv.annotations.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {conv.annotations.map((annotation, i) => (
                    <Badge key={i} variant="secondary" className="text-xs">
                      <Icon name="RiLightbulbLine" className="mr-1 h-3 w-3" />
                      {annotation}
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </div>
        </Card>
      ))}
      
      {!isSubscribed && conversations.length >= 5 && (
        <Alert className="mt-8">
          <Icon name="RiLockLine" className="h-4 w-4" />
          <AlertDescription className="ml-2">
            <strong>{t("subscriptionRequired")}</strong> {t("subscriptionMessage", { count: conversations.length + 3 })}
            <Button variant="link" className="ml-2 h-auto p-0">
              {t("upgradeToPro")}
            </Button>
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}