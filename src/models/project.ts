import { db } from "@/db";
import { projects, conversations } from "@/db/schema";
import { eq, and, desc, sql, like, or, isNull } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

export interface CreateProjectData {
  userUuid: string;
  title: string;
  description?: string;
  thumbnailUrl?: string;
  tags?: string[];
  promptsCount?: number;
  devTime?: string;
  status?: string;
}

export interface UpdateProjectData {
  title?: string;
  description?: string;
  thumbnailUrl?: string;
  tags?: string[];
  promptsCount?: number;
  devTime?: string;
  likesCount?: number;
  viewsCount?: number;
  status?: string;
}

export interface ProjectFilters {
  search?: string;
  tag?: string;
  status?: string;
  userUuid?: string;
}

export interface PaginationOptions {
  page: number;
  limit: number;
  sort?: "latest" | "popular" | "prompts" | "likes";
}

// Create a new project
export async function createProject(data: CreateProjectData) {
  const uuid = uuidv4();
  const now = new Date();

  const [project] = await db
    .insert(projects)
    .values({
      uuid,
      userUuid: data.userUuid,
      title: data.title,
      description: data.description || null,
      thumbnailUrl: data.thumbnailUrl || null,
      tags: data.tags ? JSON.stringify(data.tags) : null,
      promptsCount: data.promptsCount || 0,
      devTime: data.devTime || null,
      status: data.status || "draft",
      createdAt: now,
      updatedAt: now,
    })
    .returning();

  return project;
}

// Get project by UUID
export async function findProjectByUuid(uuid: string) {
  const [project] = await db
    .select()
    .from(projects)
    .where(and(eq(projects.uuid, uuid), isNull(projects.deletedAt)));

  return project;
}

// Get projects with pagination and filters
export async function findProjects(
  filters: ProjectFilters = {},
  pagination: PaginationOptions
) {
  const { page, limit, sort = "latest" } = pagination;
  const offset = (page - 1) * limit;

  let query = db.select().from(projects);
  
  // Build where conditions
  const conditions = [
    eq(projects.status, "published"),
    isNull(projects.deletedAt),
  ];

  if (filters.search) {
    conditions.push(
      or(
        like(projects.title, `%${filters.search}%`),
        like(projects.description, `%${filters.search}%`)
      )!
    );
  }

  if (filters.tag) {
    conditions.push(like(projects.tags, `%"${filters.tag}"%`));
  }

  if (filters.userUuid) {
    conditions.push(eq(projects.userUuid, filters.userUuid));
  }

  query = query.where(and(...conditions));

  // Apply sorting
  switch (sort) {
    case "popular":
      query = query.orderBy(desc(projects.viewsCount));
      break;
    case "prompts":
      query = query.orderBy(desc(projects.promptsCount));
      break;
    case "likes":
      query = query.orderBy(desc(projects.likesCount));
      break;
    case "latest":
    default:
      query = query.orderBy(desc(projects.createdAt));
      break;
  }

  // Apply pagination
  query = query.limit(limit).offset(offset);

  const result = await query;
  
  // Get total count for pagination
  const [{ count }] = await db
    .select({ count: sql`count(*)::int` })
    .from(projects)
    .where(and(...conditions));

  return {
    data: result,
    total: count,
    page,
    limit,
    totalPages: Math.ceil(count / limit),
  };
}

// Update project
export async function updateProject(uuid: string, data: UpdateProjectData) {
  const updatedData: any = {
    ...data,
    updatedAt: new Date(),
  };

  if (data.tags) {
    updatedData.tags = JSON.stringify(data.tags);
  }

  const [updated] = await db
    .update(projects)
    .set(updatedData)
    .where(eq(projects.uuid, uuid))
    .returning();

  return updated;
}

// Increment view count
export async function incrementProjectViews(uuid: string) {
  await db
    .update(projects)
    .set({
      viewsCount: sql`${projects.viewsCount} + 1`,
    })
    .where(eq(projects.uuid, uuid));
}

// Soft delete project
export async function deleteProject(uuid: string) {
  const [deleted] = await db
    .update(projects)
    .set({
      status: "deleted",
      deletedAt: new Date(),
    })
    .where(eq(projects.uuid, uuid))
    .returning();

  return deleted;
}