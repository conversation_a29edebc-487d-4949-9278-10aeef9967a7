import { db } from "@/db";
import { conversations } from "@/db/schema";
import { eq, and, asc } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

export interface CreateConversationData {
  projectUuid: string;
  sequence: number;
  role: "user" | "assistant";
  content: string;
  code?: string;
  annotations?: string[];
}

export interface ConversationWithLimit {
  data: any[];
  hasMore: boolean;
  totalCount: number;
}

// Create a new conversation
export async function createConversation(data: CreateConversationData) {
  const uuid = uuidv4();

  const [conversation] = await db
    .insert(conversations)
    .values({
      uuid,
      projectUuid: data.projectUuid,
      sequence: data.sequence,
      role: data.role,
      content: data.content,
      code: data.code || null,
      annotations: data.annotations ? JSON.stringify(data.annotations) : null,
      createdAt: new Date(),
    })
    .returning();

  return conversation;
}

// Create multiple conversations (batch insert)
export async function createConversations(data: CreateConversationData[]) {
  const values = data.map((conv) => ({
    uuid: uuidv4(),
    projectUuid: conv.projectUuid,
    sequence: conv.sequence,
    role: conv.role,
    content: conv.content,
    code: conv.code || null,
    annotations: conv.annotations ? JSON.stringify(conv.annotations) : null,
    createdAt: new Date(),
  }));

  const created = await db.insert(conversations).values(values).returning();
  return created;
}

// Get conversations by project UUID
export async function findConversationsByProject(
  projectUuid: string,
  limit?: number
): Promise<ConversationWithLimit> {
  let query = db
    .select()
    .from(conversations)
    .where(eq(conversations.projectUuid, projectUuid))
    .orderBy(asc(conversations.sequence));

  const allConversations = await query;
  const totalCount = allConversations.length;

  // If no limit specified, return all conversations
  if (!limit) {
    return {
      data: allConversations.map(parseConversation),
      hasMore: false,
      totalCount,
    };
  }

  // Apply limit
  const limitedConversations = allConversations.slice(0, limit);
  
  return {
    data: limitedConversations.map(parseConversation),
    hasMore: totalCount > limit,
    totalCount,
  };
}

// Get single conversation by UUID
export async function findConversationByUuid(uuid: string) {
  const [conversation] = await db
    .select()
    .from(conversations)
    .where(eq(conversations.uuid, uuid));

  return conversation ? parseConversation(conversation) : null;
}

// Delete conversations by project UUID
export async function deleteConversationsByProject(projectUuid: string) {
  await db
    .delete(conversations)
    .where(eq(conversations.projectUuid, projectUuid));
}

// Helper function to parse JSON fields
function parseConversation(conv: any) {
  return {
    ...conv,
    annotations: conv.annotations ? JSON.parse(conv.annotations) : [],
  };
}