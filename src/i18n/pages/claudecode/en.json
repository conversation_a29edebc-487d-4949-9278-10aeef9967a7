{"template": "shipany-template-one", "theme": "dark", "header": {"brand": {"title": "<PERSON>", "logo": {"src": "/logo.png", "alt": "<PERSON>"}, "url": "/"}, "nav": {"items": [{"title": "Explore", "url": "/projects", "icon": "RiCompassDiscoverLine"}, {"title": "Showcases", "url": "/#showcases", "icon": "RiApps2Line"}, {"title": "Learn", "url": "/#learn", "icon": "RiBookOpenLine"}, {"title": "Pricing", "url": "/#pricing", "icon": "RiMoneyDollarCircleLine"}]}, "buttons": [{"title": "Submit Project", "url": "/submit", "target": "_self", "variant": "default", "icon": "RiUploadLine"}, {"title": "Join Community", "url": "#", "target": "_blank", "variant": "outline", "icon": "RiTeamLine"}], "show_sign": true, "show_theme": true, "show_locale": true}, "hero": {"title": "Code is <PERSON><PERSON><PERSON>, Show Me the Talk", "highlight_text": "Show Me the Talk", "description": "Explore the Art of AI Programming Through Prompts.<br/>Discover how developers craft Claude Code projects with natural language.", "announcement": {"label": "NEW", "title": "🚀 1000+ Projects Live", "url": "/#showcases"}, "tip": "🎯 Learn from real-world prompts", "buttons": [{"title": "Start Exploring", "icon": "RiSearchLine", "url": "/projects", "target": "_self", "variant": "default"}, {"title": "View Popular Projects", "icon": "RiFireLine", "url": "/#showcases", "target": "_self", "variant": "outline"}], "show_happy_users": true, "show_badge": true}, "stats": {"name": "stats", "label": "Impact", "title": "Transforming How We Code", "description": "Real numbers from the Claude Code community", "icon": "RiLineChartLine", "items": [{"title": "Projects Generated", "label": "1,234+", "description": "And counting"}, {"title": "Dev Hours Saved", "label": "50K+", "description": "Estimated time"}, {"title": "Active Engineers", "label": "500+", "description": "Prompt masters"}]}, "feature": {"name": "feature", "title": "Why Claude Code Show?", "description": "A platform designed to showcase the intersection of AI and human creativity in programming.", "items": [{"title": "Real Project Showcases", "description": "Browse actual projects built with Claude Code, complete with source code and live demos.", "icon": "RiCodeBoxLine"}, {"title": "Full Conversation History", "description": "See the complete dialogue between developers and <PERSON>, understanding the thought process.", "icon": "RiChat3Line"}, {"title": "Prompt Templates", "description": "Discover and save effective prompt patterns for different programming scenarios.", "icon": "RiFileCopyLine"}, {"title": "Learning Resources", "description": "From beginner to expert, follow structured paths to master AI-assisted development.", "icon": "RiGraduationCapLine"}, {"title": "Community Insights", "description": "Learn from annotations and tips shared by experienced prompt engineers.", "icon": "RiLightbulbLine"}, {"title": "Code Analytics", "description": "Understand what makes a prompt effective with AI-powered analysis and metrics.", "icon": "RiBarChartBoxLine"}]}, "dialogue": {"name": "dialogue", "title": "Experience AI Conversations", "label": "Interactive Demo", "description": "See how developers transform ideas into code through natural dialogue", "items": [{"type": "user", "content": "Create a React component for a todo list with add, delete, and mark complete features", "timestamp": "10:23 AM"}, {"type": "assistant", "content": "I'll create a modern React todo list component with all the features you requested...", "timestamp": "10:23 AM", "code": "// TodoList component implementation\nconst TodoList = () => {\n  // Component logic here\n}", "annotations": ["Key insight: Start with state management", "Best practice: Use React hooks"]}]}, "showcase": {"name": "showcase", "label": "Featured", "title": "Popular Claude <PERSON>", "description": "Explore amazing projects built through AI conversations", "items": [{"title": "E-commerce Platform", "description": "Full-stack online store with payment integration built in 2 hours", "tags": ["React", "Node.js", "Stripe"], "image": {"src": "/imgs/showcases/1.png"}, "metrics": {"prompts": 12, "time": "2h", "likes": 234}}, {"title": "AI Chat Interface", "description": "Beautiful chat UI with streaming responses and markdown support", "tags": ["Next.js", "Tailwind", "AI"], "image": {"src": "/imgs/showcases/2.png"}, "metrics": {"prompts": 8, "time": "1h", "likes": 189}}, {"title": "Data Dashboard", "description": "Real-time analytics dashboard with interactive charts", "tags": ["React", "D3.js", "WebSocket"], "image": {"src": "/imgs/showcases/3.png"}, "metrics": {"prompts": 15, "time": "3h", "likes": 312}}]}, "learning": {"name": "learning", "title": "Master AI Programming", "label": "Learning Path", "description": "From novice to prompt engineering expert", "items": [{"level": "<PERSON><PERSON><PERSON>", "title": "Getting Started with <PERSON>", "description": "Learn the basics of conversing with AI for code generation", "lessons": 5, "duration": "2 hours", "icon": "RiSeedlingLine"}, {"level": "Intermediate", "title": "Effective Prompt Patterns", "description": "Master advanced techniques for complex projects", "lessons": 8, "duration": "4 hours", "icon": "RiPlantLine"}, {"level": "Expert", "title": "Prompt Engineering Mastery", "description": "Build production-ready applications with AI", "lessons": 12, "duration": "8 hours", "icon": "RiTreeLine"}]}, "testimonial": {"name": "testimonial", "label": "Reviews", "title": "What Developers Say", "description": "Hear from the community about their experience with <PERSON>", "icon": "RiStarFill", "items": [{"title": "<PERSON>", "label": "Full-stack Developer", "description": "<PERSON> opened my eyes to what's possible with AI programming. The real project examples helped me improve my prompting skills dramatically.", "image": {"src": "/imgs/users/1.png"}}, {"title": "<PERSON>", "label": "Startup Founder", "description": "We built our MVP in days instead of months. The prompt templates on Claude Code Show were invaluable for our rapid development.", "image": {"src": "/imgs/users/2.png"}}, {"title": "<PERSON>", "label": "AI Enthusiast", "description": "The conversation histories are gold! Seeing how experienced developers interact with <PERSON> taught me more than any tutorial.", "image": {"src": "/imgs/users/3.png"}}]}, "pricing": {"name": "pricing", "title": "Choose Your Plan", "description": "Access the full power of AI programming insights", "items": [{"title": "Free", "price": "$0", "period": "forever", "description": "Perfect for exploring", "features": ["Browse public projects", "View basic conversations", "Daily usage limits", "Community support"], "button": {"title": "Get Started", "url": "#"}}, {"title": "Pro", "price": "$9.9", "period": "/month", "description": "For serious developers", "popular": true, "features": ["Unlimited access", "Download conversations", "Advanced search filters", "Priority support", "Save prompt templates", "Code export features"], "button": {"title": "Upgrade to Pro", "url": "#", "variant": "default"}}, {"title": "Team", "price": "$29.9", "period": "/month", "description": "For development teams", "features": ["Everything in Pro", "Team collaboration", "Private project library", "API access", "Custom integrations", "Dedicated support"], "button": {"title": "Contact Sales", "url": "#"}}]}, "cta": {"name": "cta", "title": "Ready to Transform Your Development?", "description": "Join thousands of developers learning the art of AI programming.", "buttons": [{"title": "Start Exploring", "url": "#", "target": "_self", "icon": "RiRocketLine"}, {"title": "View Documentation", "url": "#", "target": "_blank", "variant": "outline"}]}, "footer": {"name": "footer", "brand": {"title": "<PERSON>", "description": "Showcasing the art of AI programming through real conversations and projects.", "logo": {"src": "/logo.png", "alt": "<PERSON>"}, "url": "/"}, "copyright": "© 2025 • Claude <PERSON> Show. All rights reserved.", "nav": {"items": [{"title": "Platform", "children": [{"title": "Explore Projects", "url": "/#explore", "target": "_self"}, {"title": "Learning Path", "url": "/#learn", "target": "_self"}, {"title": "Pricing", "url": "/#pricing", "target": "_self"}]}, {"title": "Resources", "children": [{"title": "Documentation", "url": "#", "target": "_blank"}, {"title": "API Reference", "url": "#", "target": "_blank"}, {"title": "Blog", "url": "#", "target": "_blank"}]}, {"title": "Community", "children": [{"title": "Discord", "url": "#", "target": "_blank"}, {"title": "GitHub", "url": "#", "target": "_blank"}, {"title": "Twitter", "url": "#", "target": "_blank"}]}]}, "social": {"items": [{"title": "Twitter", "icon": "RiTwitterXFill", "url": "#", "target": "_blank"}, {"title": "GitHub", "icon": "RiGithubFill", "url": "#", "target": "_blank"}, {"title": "Discord", "icon": "RiDiscordFill", "url": "#", "target": "_blank"}]}, "agreement": {"items": [{"title": "Privacy Policy", "url": "/privacy-policy"}, {"title": "Terms of Service", "url": "/terms-of-service"}]}}}